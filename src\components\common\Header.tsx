'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/base';
import { NavigationMenuItem } from './NavigationMenuItem';
import { useImageStore } from '@/store/imageStore';
import {
  downloadSingleImage,
  downloadImagesAsZip,
  getImageDownloadInfo,
} from '@/lib/imageUtils/imageDownload';
import type { SupportedFormat } from '@/lib/imageUtils/imageConvert';
import { Button } from '../ui/Button';
import { accountInfoStore } from '@/store/accountStore';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
} from '@/components/ui/DropdownMenu';
import { authUtils } from '@/lib/authUtils';
import { LanguageSwitcher } from '@/components/i18n/LanguageSwitcher';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

/**
 * 安全地将字符串转换为支持的格式
 */
function toSupportedFormat(format: string): SupportedFormat {
  const supportedFormats: SupportedFormat[] = [
    'png',
    'jpg',
    'jpeg',
    'webp',
    'bmp',
    'xbm',
    'xpm',
  ];
  return supportedFormats.includes(format as SupportedFormat)
    ? (format as SupportedFormat)
    : 'png';
}

interface HeaderProps {
  className?: string;
  variant?: 'desktop' | 'batch-editor' | 'mobile';
}

/**
 * 通用头部组件
 * 支持桌面端、批量编辑器和移动端三种变体
 */
export function Header({ className, variant }: HeaderProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 获取当前路由
  const pathname = usePathname();

  // 获取国际化翻译
  const tools = useTranslations('tools');
  const common = useTranslations('common');
  const auth = useTranslations('auth');

  // 根据当前路由确定显示的标题
  const getCurrentTitle = () => {
    if (pathname.includes('/batch-editor')) {
      return tools('batchEditor');
    }
    return tools('removeBackground');
  };

  // 移动端用户认证相关逻辑
  const userInfo = accountInfoStore(s => s.userInfo);
  const userAvatar = userInfo.head_pic || '/apps/icons/avatar.png';

  // 认证相关操作
  const router = useRouter();
  const handleLogin = () => authUtils.login();
  const handleRegister = () => authUtils.register();
  const handleLogout = () => authUtils.logout();
  const handleAccountCenter = () => router.push('/account-page');

  // 获取未锁定的图片数量，排除被锁定的图片
  const imageCount = useImageStore(
    state =>
      Array.from(state.images.values()).filter(
        image => image.status !== 'locked'
      ).length
  );

  // 在下载时才获取具体的图片数据，过滤掉锁定的图片
  const getImages = useCallback(() => {
    return Array.from(useImageStore.getState().images.values()).filter(
      image => image.status !== 'locked'
    );
  }, []);

  const handleDownload = async () => {
    const images = getImages();

    if (images.length === 0) {
      alert(common('noDownloadableImages'));
      return;
    }

    setIsDownloading(true);

    try {
      if (images.length === 1) {
        // 单张图片直接下载
        const image = images[0];
        const downloadInfo = await getImageDownloadInfo(image);

        await downloadSingleImage(
          downloadInfo.url,
          downloadInfo.fileName,
          toSupportedFormat(downloadInfo.format)
        );
      } else {
        // 多张图片打包下载 - 需要并发处理所有图片的合成
        const downloadImages = await Promise.all(
          images.map(async image => {
            const downloadInfo = await getImageDownloadInfo(image);
            return {
              url: downloadInfo.url,
              name: downloadInfo.fileName,
              format: toSupportedFormat(downloadInfo.format),
            };
          })
        );

        await downloadImagesAsZip(
          downloadImages,
          `pixpretty_images_${new Date().toISOString().slice(0, 10)}`
        );
      }
    } catch (error) {
      console.error('下载失败:', error);
      alert(common('downloadFailed'));
    } finally {
      setIsDownloading(false);
    }
  };

  // 导航菜单配置
  const NAVIGATION_ITEMS = [
    {
      title: tools('removeBackground'),
      path: '/remove-background',
    },
    {
      title: tools('batchEditor'),
      path: '/batch-editor',
    },
  ] as const;

  // 统一的渲染结构
  return (
    <header
      className={cn(
        'border-b border-gray-200 bg-white',
        variant === 'mobile' ? 'px-4 py-3' : '',
        className
      )}
    >
      <div
        className={variant === 'mobile' ? '' : 'mx-auto px-4 sm:px-6 lg:px-8'}
      >
        <div
          className={cn(
            'flex justify-between items-center',
            variant === 'mobile' ? '' : 'h-16'
          )}
        >
          {/* Logo */}
          <div className='flex items-center'>
            {variant === 'mobile' ? (
              // 移动端简单Logo
              <Image src='/apps/logo.svg' width={80} height={20} alt='logo' />
            ) : (
              // 桌面端Logo区域
              <div className='flex items-center space-x-6'>
                {/* PixPretty Logo */}
                <div className='flex items-center space-x-2'>
                  <Image
                    src='/apps/logo.svg'
                    width={140}
                    height={32}
                    alt='logo'
                  />
                </div>

                {/* 分隔线 */}
                <div className='bg-[#e7e7e7] h-[18px] w-px' />
                <DropdownMenu onOpenChange={setIsDropdownOpen}>
                  <DropdownMenuTrigger className='cursor-pointer'>
                    {/* Remove BG 下拉菜单 */}
                    <div className='flex items-center space-x-2'>
                      <span className='text-base font-medium text-text-primary'>
                        {getCurrentTitle()}
                      </span>
                      <div className='relative size-4'>
                        <Image
                          src='/apps/icons/down.svg'
                          width={16}
                          height={16}
                          alt='down'
                          className={`transition-transform duration-300 ease-in-out ${
                            isDropdownOpen ? 'rotate-180' : 'rotate-0'
                          }`}
                        />
                      </div>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className='w-50 p-2 bg-white border border-[#E7E7E7] rounded-xl shadow-[0px_7px_14px_0px_rgba(220,223,228,0.16),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_10px_32px_0px_rgba(220,223,228,0.08)] space-y-1'
                    align='start'
                    sideOffset={24}
                    alignOffset={-34}
                  >
                    {NAVIGATION_ITEMS.map(item => (
                      <NavigationMenuItem
                        key={item.path}
                        title={item.title}
                        path={item.path}
                      />
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>

          {/* 用户操作按钮 */}
          <div
            className={cn(
              'flex items-center',
              variant === 'mobile' ? 'space-x-2' : 'space-x-3'
            )}
          >
            {/* Download 按钮 */}
            {variant === 'batch-editor' && (
              <Button
                onClick={handleDownload}
                disabled={isDownloading || imageCount === 0}
                className={cn('h-9 px-4 ', {
                  'opacity-50 cursor-not-allowed':
                    isDownloading || imageCount === 0,
                  'min-w-[96px]': isDownloading,
                })}
              >
                <div className='flex items-center gap-2 h-8'>
                  <Image
                    src='/apps/icons/dowload.svg'
                    width={24}
                    height={24}
                    alt='dowload'
                  />
                  <span>{common('download')}</span>
                  {imageCount > 1 && <span>{imageCount}</span>}
                </div>
              </Button>
            )}

            {/* 语言切换器 */}
            <LanguageSwitcher variant='select' className='mr-3' />

            {/* 统一的用户认证按钮 */}
            {userInfo?.member_level ? (
              <DropdownMenu>
                <DropdownMenuTrigger className='cursor-pointer'>
                  <Image
                    src={userAvatar}
                    alt='avatar'
                    width={variant === 'mobile' ? 28 : 32}
                    height={variant === 'mobile' ? 28 : 32}
                    className={variant === 'mobile' ? '' : 'rounded-full'}
                    unoptimized={true}
                  />
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className='mr-4 w-[312px] p-0 bg-white border border-[#E7E7E7] rounded-xl shadow-[0px_7px_14px_0px_rgba(220,223,228,0.16),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_10px_32px_0px_rgba(220,223,228,0.08)]'
                  sideOffset={14}
                >
                  <div className='px-2 py-3'>
                    {/* 用户邮箱区域 - 黄色高亮背景 */}
                    <div className='border-b border-[#E7E7E7] px-3 py-2 mb-2'>
                      <div className='flex items-center gap-1'>
                        <span className='text-black text-base leading-6'>
                          {userInfo.email}
                        </span>
                      </div>
                    </div>

                    {/* 菜单项容器 */}
                    <div className='flex flex-col gap-1'>
                      {/* Credits 项 - 灰色背景 */}
                      <div className='rounded-lg px-3 py-2 hover:bg-[#F0F0F0]'>
                        <div className='flex items-center gap-2 h-8'>
                          <Image
                            src='/apps/icons/score.svg'
                            alt='score'
                            width={24}
                            height={24}
                          />
                          <span className='text-black text-base leading-6'>
                            {common('credits')}: {userInfo.score}
                          </span>
                        </div>
                      </div>

                      {/* My account 项 - 白色背景 */}
                      <div
                        className='rounded-lg px-3 py-2 cursor-pointer hover:bg-[#F0F0F0]'
                        onClick={handleAccountCenter}
                      >
                        <div className='flex items-center gap-2 h-8'>
                          <Image
                            src='/apps/icons/account.svg'
                            alt='account'
                            width={24}
                            height={24}
                          />
                          <span className='text-black text-base leading-6'>
                            {auth('myAccount')}
                          </span>
                        </div>
                      </div>

                      {/* Logout 项 - 白色背景 */}
                      <div
                        className='rounded-lg px-3 py-2 cursor-pointer hover:bg-[#F0F0F0]'
                        onClick={handleLogout}
                      >
                        <div className='flex items-center gap-2 h-8'>
                          <Image
                            src='/apps/icons/logout.svg'
                            alt='logout'
                            width={24}
                            height={24}
                          />
                          <span className='text-black text-base leading-6'>
                            {auth('signOut')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <>
                <Button
                  className='h-9'
                  variant={'outline'}
                  onClick={handleRegister}
                >
                  {auth('signUp')}
                </Button>
                <Button className='h-9' onClick={handleLogin}>
                  {auth('signIn')}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
